import React from "react";
import ReactDOM from "react-dom/client";
import CleanChatWidget from "../components/chat/CleanChatWidget";
import "../styles/widget-isolated.css"; // Import isolated widget CSS only

// Widget configuration interface (simplified for the original component)
export interface ChatWidgetConfig {
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  zIndex?: number;
  autoOpen?: boolean;
}

// Default configuration
const defaultConfig: ChatWidgetConfig = {
  position: "bottom-right",
  zIndex: 9999,
  autoOpen: false,
};

// Main widget class
class SimpleChatWidget {
  private config: ChatWidgetConfig;
  private container: HTMLElement | null = null;
  private root: ReactDOM.Root | null = null;
  private isInitialized = false;

  constructor(config: Partial<ChatWidgetConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  // Initialize the widget
  init(targetElement?: HTMLElement | string): void {
    if (this.isInitialized) {
      console.warn("SimpleChatWidget is already initialized");
      return;
    }

    // Find or create container
    if (targetElement) {
      if (typeof targetElement === "string") {
        this.container = document.querySelector(targetElement);
      } else {
        this.container = targetElement;
      }
    } else {
      this.container = this.createContainer();
      document.body.appendChild(this.container);
    }

    if (!this.container) {
      throw new Error("Could not find or create container element");
    }

    // Create React root and render
    this.root = ReactDOM.createRoot(this.container);
    this.render();
    this.isInitialized = true;

    // Auto-open if configured
    if (this.config.autoOpen) {
      setTimeout(() => this.open(), 1000);
    }
  }

  // Create default container
  private createContainer(): HTMLElement {
    const container = document.createElement("div");
    container.id = "simple-chat-widget-container";
    container.style.cssText = this.getContainerStyles();
    return container;
  }

  // Get container styles based on position
  private getContainerStyles(): string {
    const { zIndex } = this.config;
    return `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: ${zIndex};
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
  }

  // Render the React component
  private render(): void {
    if (!this.root) return;

    // Render the original SimpleChatWindow component
    this.root.render(
      React.createElement(
        "div",
        {
          style: { pointerEvents: "auto" },
        },
        React.createElement(SimpleChatWindow)
      )
    );
  }

  // Dispatch custom events
  private dispatchEvent(type: string, detail: any): void {
    const event = new CustomEvent(`simpleChatWidget:${type}`, { detail });
    window.dispatchEvent(event);
  }

  // Public API methods (simplified since we're using the original component)
  show(): void {
    if (this.container) {
      this.container.style.display = "block";
    }
  }

  hide(): void {
    if (this.container) {
      this.container.style.display = "none";
    }
  }

  updateConfig(newConfig: Partial<ChatWidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    if (this.container) {
      this.container.style.cssText = this.getContainerStyles();
    }
  }

  destroy(): void {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.container = null;
    this.isInitialized = false;
  }
}

// Global API
declare global {
  interface Window {
    SimpleChatWidget: typeof SimpleChatWidget;
    simpleChatWidget?: SimpleChatWidget;
  }
}

// Export for module usage
export { SimpleChatWidget };
export type { ChatWidgetConfig };

// Global initialization for script tag usage
if (typeof window !== "undefined") {
  window.SimpleChatWidget = SimpleChatWidget;

  // Auto-initialize if script has data attributes
  document.addEventListener("DOMContentLoaded", () => {
    const script = document.querySelector('script[src*="simple-chat-widget"]');
    if (script?.hasAttribute("data-auto-init")) {
      const config: Partial<ChatWidgetConfig> = {};

      // Parse data attributes
      const attrs = ["position", "z-index", "auto-open"];

      attrs.forEach((attr) => {
        const value = script.getAttribute(`data-${attr}`);
        if (value !== null) {
          const key = attr.replace(/-([a-z])/g, (_, c) =>
            c.toUpperCase()
          ) as keyof ChatWidgetConfig;
          if (attr === "auto-open") {
            (config as any)[key] = value === "true";
          } else if (attr === "z-index") {
            (config as any).zIndex = parseInt(value, 10);
          } else {
            (config as any)[key] = value;
          }
        }
      });

      window.simpleChatWidget = new SimpleChatWidget(config);
      window.simpleChatWidget.init();
    }
  });
}
