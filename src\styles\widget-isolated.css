/* Isolated Chat Widget Styles - Completely scoped to prevent conflicts */

/* Widget namespace container */
.simple-chat-widget {
  /* CSS Reset for widget scope */
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 9999;
  pointer-events: none;
  box-sizing: border-box;
  line-height: 1.5;
  color: #1f2937;
}

/* Reset all child elements */
.simple-chat-widget *,
.simple-chat-widget *::before,
.simple-chat-widget *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  background: transparent;
  text-decoration: none;
  list-style: none;
}

.simple-chat-widget .interactive {
  pointer-events: auto;
}

/* Chat toggle button */
.simple-chat-widget .chat-toggle-button {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  pointer-events: auto;
  font-family: inherit;
  outline: none;
}

.simple-chat-widget .chat-toggle-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
}

.simple-chat-widget .chat-toggle-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.simple-chat-widget .chat-toggle-button svg {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
  stroke: currentColor;
}

/* Chat window */
.simple-chat-widget .chat-window {
  position: absolute;
  bottom: 5rem;
  right: 0;
  width: 24rem;
  height: 31.25rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  pointer-events: auto;
  animation: slideInUp 0.3s ease-out;
  font-family: inherit;
  color: #1f2937;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chat header */
.simple-chat-widget .chat-header {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.simple-chat-widget .chat-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.simple-chat-widget .chat-header-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.simple-chat-widget .chat-header-avatar svg {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
}

.simple-chat-widget .chat-header-info h3 {
  font-weight: 600;
  margin: 0;
  font-size: 1rem;
  font-family: inherit;
  color: white;
}

.simple-chat-widget .chat-header-info p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-family: inherit;
}

.simple-chat-widget .chat-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  font-family: inherit;
  outline: none;
}

.simple-chat-widget .chat-close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.simple-chat-widget .chat-close-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.simple-chat-widget .chat-close-button svg {
  width: 1.25rem;
  height: 1.25rem;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* Welcome form */
.simple-chat-widget .welcome-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.simple-chat-widget .welcome-content {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.simple-chat-widget .welcome-title {
  text-align: center;
}

.simple-chat-widget .welcome-title h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  font-family: inherit;
}

.simple-chat-widget .welcome-title p {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
  font-family: inherit;
}

.simple-chat-widget .form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.simple-chat-widget .form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s;
  outline: none;
  font-family: inherit;
  background: white;
  color: #1f2937;
}

.simple-chat-widget .form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.simple-chat-widget .form-input::placeholder {
  color: #9ca3af;
}

.simple-chat-widget .start-chat-button {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.75rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: inherit;
  font-size: 0.875rem;
  outline: none;
}

.simple-chat-widget .start-chat-button.enabled {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.simple-chat-widget .start-chat-button.enabled:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
}

.simple-chat-widget .start-chat-button.enabled:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.simple-chat-widget .start-chat-button.disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Chat interface */
.simple-chat-widget .chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.simple-chat-widget .chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.simple-chat-widget .chat-messages::-webkit-scrollbar {
  width: 4px;
}

.simple-chat-widget .chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.simple-chat-widget .chat-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.simple-chat-widget .chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Empty state */
.simple-chat-widget .empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.simple-chat-widget .empty-state-icon {
  width: 4rem;
  height: 4rem;
  background: #dbeafe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.simple-chat-widget .empty-state-icon svg {
  width: 2rem;
  height: 2rem;
  color: #3b82f6;
  fill: currentColor;
}

.simple-chat-widget .empty-state p {
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  font-family: inherit;
}

.simple-chat-widget .empty-state .subtitle {
  font-size: 0.875rem;
  color: #9ca3af;
  font-family: inherit;
}

/* Chat input */
.simple-chat-widget .chat-input-container {
  padding: 1rem;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.simple-chat-widget .chat-input-wrapper {
  flex: 1;
}

.simple-chat-widget .chat-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 1.5rem;
  font-size: 0.875rem;
  outline: none;
  transition: all 0.2s;
  font-family: inherit;
  background: white;
  color: #1f2937;
}

.simple-chat-widget .chat-input:focus {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.simple-chat-widget .chat-input:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.simple-chat-widget .chat-input::placeholder {
  color: #9ca3af;
}

.simple-chat-widget .send-button {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-family: inherit;
  outline: none;
}

.simple-chat-widget .send-button.enabled {
  background: #3b82f6;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.simple-chat-widget .send-button.enabled:hover {
  background: #1d4ed8;
  transform: scale(1.05);
}

.simple-chat-widget .send-button.enabled:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.simple-chat-widget .send-button.disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

.simple-chat-widget .send-button svg {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
}

/* Loading spinner */
.simple-chat-widget .loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Status indicator */
.simple-chat-widget .status-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  display: inline-block;
}

.simple-chat-widget .status-indicator.connected {
  background: #10b981;
}

.simple-chat-widget .status-indicator.connecting {
  background: #f59e0b;
}

/* Message bubbles */
.simple-chat-widget .message-bubble {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  line-height: 1.4;
  font-family: inherit;
  word-wrap: break-word;
}

.simple-chat-widget .message-bubble.user {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 0.25rem;
}

.simple-chat-widget .message-bubble.other {
  background: white;
  color: #1f2937;
  border: 1px solid #e5e7eb;
  margin-right: auto;
  border-bottom-left-radius: 0.25rem;
}

.simple-chat-widget .message-meta {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
  font-family: inherit;
}

/* Responsive design */
@media (max-width: 768px) {
  .simple-chat-widget {
    bottom: 0.5rem;
    right: 0.5rem;
  }

  .simple-chat-widget .chat-window {
    width: calc(100vw - 1rem);
    max-width: 20rem;
    height: 28rem;
  }

  .simple-chat-widget .chat-toggle-button {
    width: 3.5rem;
    height: 3.5rem;
  }

  .simple-chat-widget .chat-toggle-button svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

@media (max-width: 480px) {
  .simple-chat-widget .chat-window {
    width: calc(100vw - 1rem);
    height: 25rem;
    bottom: 4rem;
  }

  .simple-chat-widget .welcome-content {
    padding: 1rem;
    gap: 1rem;
  }

  .simple-chat-widget .chat-messages {
    padding: 0.75rem;
  }

  .simple-chat-widget .chat-input-container {
    padding: 0.75rem;
  }
}

/* Accessibility improvements */
.simple-chat-widget button:focus,
.simple-chat-widget input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .simple-chat-widget .chat-window {
    border: 2px solid currentColor;
  }

  .simple-chat-widget .chat-toggle-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .simple-chat-widget * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
