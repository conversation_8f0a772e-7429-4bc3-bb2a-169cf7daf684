import React from "react";
import { useMatrixChat } from "../../hooks/useMatrixChat";
import MessengerChatBubble from "./MessengerChatBubble";
import LoadingSpinner from "../ui/LoadingSpinner";

// Clean wrapper component for widget - no external dependencies
const CleanChatWidget: React.FC = () => {
  const {
    chatState,
    userInfo,
    messages,
    newMessage,
    setNewMessage,
    containerRef,
    toggleChat,
    updateUserInfo,
    startChat,
    sendMessage,
  } = useMatrixChat();

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (chatState.isStarted) {
        sendMessage();
      } else if (userInfo.name.trim()) {
        startChat();
      }
    }
  };

  const isFormValid = userInfo.name.trim().length > 0;

  return (
    <div className="simple-chat-widget">
      {/* Chat toggle button */}
      <button
        onClick={toggleChat}
        className="chat-toggle-button interactive"
        aria-label="Toggle chat support"
      >
        {chatState.isOpen ? (
          <svg
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </button>

      {/* Chat window */}
      {chatState.isOpen && (
        <div className="chat-window interactive">
          {!chatState.isStarted ? (
            /* Welcome form */
            <div className="welcome-form">
              {/* Header */}
              <div className="chat-header">
                <div className="chat-header-content">
                  <div className="chat-header-avatar">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="chat-header-info">
                    <h3>Hỗ trợ khách hàng</h3>
                    <p>Luôn sẵn sàng hỗ trợ bạn</p>
                  </div>
                </div>
                <button
                  onClick={toggleChat}
                  className="chat-close-button"
                  aria-label="Close chat"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Form content */}
              <div className="welcome-content">
                <div className="welcome-title">
                  <h4>Xin chào! 👋</h4>
                  <p>
                    Chúng tôi sẵn sàng hỗ trợ bạn. Vui lòng cho biết tên để bắt
                    đầu cuộc trò chuyện.
                  </p>
                </div>

                <div className="form-fields">
                  <input
                    type="text"
                    placeholder="Tên của bạn *"
                    value={userInfo.name}
                    onChange={(e) => updateUserInfo("name", e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="form-input"
                  />

                  <input
                    type="text"
                    placeholder="Số điện thoại (tùy chọn)"
                    value={userInfo.phone}
                    onChange={(e) => updateUserInfo("phone", e.target.value)}
                    className="form-input"
                  />

                  <input
                    type="text"
                    placeholder="Chủ đề cần hỗ trợ (tùy chọn)"
                    value={userInfo.topicName}
                    onChange={(e) =>
                      updateUserInfo("topicName", e.target.value)
                    }
                    className="form-input"
                  />
                </div>

                <button
                  onClick={startChat}
                  disabled={!isFormValid || chatState.isLoading}
                  className={`start-chat-button ${
                    isFormValid && !chatState.isLoading ? "enabled" : "disabled"
                  }`}
                >
                  {chatState.isLoading ? (
                    <>
                      <div className="loading-spinner"></div>
                      <span>Đang kết nối...</span>
                    </>
                  ) : (
                    "Bắt đầu trò chuyện"
                  )}
                </button>
              </div>
            </div>
          ) : (
            /* Chat interface */
            <div className="chat-interface">
              {/* Header */}
              <div className="chat-header">
                <div className="chat-header-content">
                  <div className="chat-header-avatar">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="chat-header-info">
                    <h3>Hỗ trợ khách hàng</h3>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <div
                        className={`status-indicator ${
                          chatState.isConnected ? "connected" : "connecting"
                        }`}
                      ></div>
                      <p>
                        {chatState.isConnected
                          ? "Đang hoạt động"
                          : "Đang kết nối..."}
                      </p>
                    </div>
                  </div>
                </div>
                <button
                  onClick={toggleChat}
                  className="chat-close-button"
                  aria-label="Close chat"
                >
                  <svg viewBox="0 0 24 24">
                    <path d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Messages */}
              <div ref={containerRef} className="chat-messages">
                {messages.length === 0 ? (
                  <div className="empty-state">
                    <div className="empty-state-icon">
                      <svg viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p>Chào mừng bạn đến với hỗ trợ!</p>
                    <p className="subtitle">
                      Hãy gửi tin nhắn để bắt đầu cuộc trò chuyện
                    </p>
                  </div>
                ) : (
                  messages.map((msg) => (
                    <MessengerChatBubble key={msg.eventId} message={msg} />
                  ))
                )}
              </div>

              {/* Input */}
              <div className="chat-input-container">
                <div className="chat-input-wrapper">
                  <input
                    type="text"
                    placeholder="Nhập tin nhắn..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    disabled={!chatState.isConnected || chatState.isLoading}
                    className="chat-input"
                  />
                </div>
                <button
                  onClick={sendMessage}
                  disabled={
                    !newMessage.trim() ||
                    !chatState.isConnected ||
                    chatState.isLoading
                  }
                  className={`send-button ${
                    newMessage.trim() &&
                    chatState.isConnected &&
                    !chatState.isLoading
                      ? "enabled"
                      : "disabled"
                  }`}
                >
                  {chatState.isLoading ? (
                    <div className="loading-spinner"></div>
                  ) : (
                    <svg viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CleanChatWidget;
